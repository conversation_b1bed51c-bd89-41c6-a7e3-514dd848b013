/* eslint-disable @typescript-eslint/naming-convention */
import * as vscode from 'vscode';
import * as fetchH2 from 'fetch-h2';

export interface CodestralCompletionRequest {
    model: string;
    prompt: string;
    suffix?: string;
    max_tokens?: number;
    temperature?: number;
    top_p?: number;
    min_tokens?: number;
    stop?: string[];
    random_seed?: number;
}

export interface CodestralCompletionResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

export interface CodestralChatRequest {
    model: string;
    messages: Array<{
        role: string;
        content: string;
    }>;
    max_tokens?: number;
    temperature?: number;
    top_p?: number;
    stream?: boolean;
    safe_prompt?: boolean;
    random_seed?: number;
}

export class CodestralAPI {
    private static readonly COMPLETION_ENDPOINT = "https://codestral.mistral.ai/v1/fim/completions";
    private static readonly CHAT_ENDPOINT = "https://api.mistral.ai/v1/chat/completions";
    private static readonly DEFAULT_API_KEY = "codestral-mamba-2407"; // Use model name as fallback

    // Rate limiting
    private static lastRequestTime = 0;
    private static readonly MIN_REQUEST_INTERVAL = 2000; // 2 seconds between requests

    private static getApiKey(): string {
        const userApiKey = vscode.workspace.getConfiguration().get<string>("amrdevai.codestralApiKey");
        return userApiKey || this.DEFAULT_API_KEY;
    }

    private static isCodestralEnabled(): boolean {
        return vscode.workspace.getConfiguration().get<boolean>("amrdevai.useCodestral") || false;
    }

    private static async waitForRateLimit(): Promise<void> {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;

        if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
            const waitTime = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
            console.log(`[AmrDev-AI] Rate limiting: waiting ${waitTime}ms`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        this.lastRequestTime = Date.now();
    }

    public static async getCodeCompletion(
        prompt: string,
        suffix?: string,
        maxTokens: number = 100,
        temperature: number = 0.1
    ): Promise<string> {
        if (!this.isCodestralEnabled()) {
            throw new Error("Codestral is not enabled. Please enable it in settings.");
        }

        const apiKey = this.getApiKey();
        if (!apiKey) {
            throw new Error("Codestral API key is not configured.");
        }

        const requestBody: CodestralCompletionRequest = {
            model: "codestral-latest",
            prompt: prompt,
            suffix: suffix,
            max_tokens: maxTokens,
            temperature: temperature,
            top_p: 1.0,
            stop: ["\n\n"]
        };

        try {
            // Apply rate limiting
            await this.waitForRateLimit();

            console.log("[AmrDev-AI] Sending Codestral completion request:", {
                endpoint: this.COMPLETION_ENDPOINT,
                model: requestBody.model,
                promptLength: prompt.length,
                maxTokens: maxTokens
            });

            const response = await fetchH2.fetch(this.COMPLETION_ENDPOINT, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${apiKey}`,
                    "User-Agent": "AmrDev-AI-VSCode/1.0"
                },
                body: JSON.stringify(requestBody),
                timeout: 30000 // 30 seconds timeout
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`[AmrDev-AI] Codestral API error ${response.status}:`, errorText);

                if (response.status === 429) {
                    throw new Error(`Rate limit exceeded. Please wait a moment before trying again.`);
                } else if (response.status === 401) {
                    throw new Error(`Invalid API key. Please check your Codestral API key in settings.`);
                } else if (response.status === 403) {
                    throw new Error(`Access forbidden. Please check your Codestral API permissions.`);
                } else {
                    throw new Error(`Codestral API error: ${response.status} ${response.statusText}`);
                }
            }

            const responseText = await response.text();
            console.log("[AmrDev-AI] Codestral raw response:", responseText.substring(0, 200));

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error("[AmrDev-AI] Failed to parse Codestral response:", parseError);
                throw new Error(`Invalid JSON response from Codestral: ${responseText.substring(0, 100)}`);
            }

            // Handle different response formats
            if (data.choices && data.choices.length > 0) {
                // Standard format
                if (data.choices[0].message && data.choices[0].message.content) {
                    return data.choices[0].message.content;
                }
                // Alternative format
                if (data.choices[0].text) {
                    return data.choices[0].text;
                }
                // Direct content
                if (typeof data.choices[0] === 'string') {
                    return data.choices[0];
                }
            }

            // If no choices, check for direct content
            if (data.content) {
                return data.content;
            }

            if (data.text) {
                return data.text;
            }

            console.warn("[AmrDev-AI] Unexpected Codestral response format:", data);
            return "";
        } catch (error) {
            console.error("Codestral completion error:", error);
            throw error;
        }
    }

    public static async getChatCompletion(
        messages: Array<{ role: string; content: string }>,
        maxTokens: number = 1000,
        temperature: number = 0.7
    ): Promise<string> {
        if (!this.isCodestralEnabled()) {
            throw new Error("Codestral is not enabled. Please enable it in settings.");
        }

        const apiKey = this.getApiKey();
        if (!apiKey) {
            throw new Error("Codestral API key is not configured.");
        }

        const requestBody: CodestralChatRequest = {
            model: "codestral-mamba-2407",
            messages: messages,
            max_tokens: maxTokens,
            temperature: temperature,
            stream: false,
            safe_prompt: false
        };

        try {
            // Apply rate limiting
            await this.waitForRateLimit();

            console.log("[AmrDev-AI] Sending Codestral chat request:", {
                endpoint: this.CHAT_ENDPOINT,
                model: requestBody.model,
                messagesCount: messages.length,
                maxTokens: maxTokens
            });

            const response = await fetchH2.fetch(this.CHAT_ENDPOINT, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${apiKey}`,
                    "User-Agent": "AmrDev-AI-VSCode/1.0"
                },
                body: JSON.stringify(requestBody),
                timeout: 30000 // 30 seconds timeout
            });

            if (!response.ok) {
                throw new Error(`Codestral API error: ${response.status} ${response.statusText}`);
            }

            const responseText = await response.text();
            console.log("[AmrDev-AI] Codestral chat raw response:", responseText.substring(0, 200));

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error("[AmrDev-AI] Failed to parse Codestral chat response:", parseError);
                throw new Error(`Invalid JSON response from Codestral: ${responseText.substring(0, 100)}`);
            }

            // Handle different response formats
            if (data.choices && data.choices.length > 0) {
                // Standard format
                if (data.choices[0].message && data.choices[0].message.content) {
                    return data.choices[0].message.content;
                }
                // Alternative format
                if (data.choices[0].text) {
                    return data.choices[0].text;
                }
                // Direct content
                if (typeof data.choices[0] === 'string') {
                    return data.choices[0];
                }
            }

            // If no choices, check for direct content
            if (data.content) {
                return data.content;
            }

            if (data.text) {
                return data.text;
            }

            console.warn("[AmrDev-AI] Unexpected Codestral chat response format:", data);
            return "";
        } catch (error) {
            console.error("Codestral chat error:", error);
            throw error;
        }
    }

    public static async testConnection(): Promise<boolean> {
        try {
            const testMessages = [
                { role: "user", content: "Hello, can you help me with coding?" }
            ];

            const response = await this.getChatCompletion(testMessages, 10, 0.1);
            return response.length > 0;
        } catch (error) {
            console.error("Codestral connection test failed:", error);
            return false;
        }
    }

    public static getStatus(): { enabled: boolean; hasApiKey: boolean; endpoint: string } {
        return {
            enabled: this.isCodestralEnabled(),
            hasApiKey: !!this.getApiKey(),
            endpoint: this.COMPLETION_ENDPOINT
        };
    }
}
