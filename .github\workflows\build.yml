name: CI build

on:
  # Trigger the workflow on pushes to only the 'main' branch (this avoids duplicate checks being run e.g. for dependabot pull requests)
  push:
    branches: [main, dev]
  # Trigger the workflow on any pull request
  pull_request:
  workflow_dispatch:
    inputs:
      lsp_branch:
        description: "Branch name of lsp"
        default: "main"
      chat_js_branch:
        description: "Branch name of chat-js"
        default: "main"

jobs:
  dist:
    strategy:
      matrix:
        include:
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            code-target: win32-x64
          - os: windows-latest
            target: aarch64-pc-windows-msvc
            code-target: win32-arm64
          - os: ubuntu-22.04
            target: x86_64-unknown-linux-gnu
            code-target: linux-x64
          - os: ubuntu-22.04
            target: aarch64-unknown-linux-gnu
            code-target: linux-arm64
          # - os: ubuntu-20.04
          #   target: armv7-unknown-linux-gnueabihf
          #   code-target: linux-armhf
          - os: macos-13
            target: x86_64-apple-darwin
            code-target: darwin-x64
          - os: macos-14
            target: aarch64-apple-darwin
            code-target: darwin-arm64

    env:
      LLM_LS_TARGET: ${{ matrix.target }}

    name: dist (${{ matrix.target }})
    runs-on: ${{ matrix.os }}
    container: ${{ matrix.container }}

    steps:
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ env.FETCH_DEPTH }}
          
      - name: Download lsp artifacts
        id: download-artifact-lsp
        uses: dawidd6/action-download-artifact@v3
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          workflow: agent_engine_build.yml
          repo: smallcloudai/refact
          branch: ${{ inputs.lsp_branch }}
          path: ./assets
          name: dist-${{ matrix.target }}
      
      - name: Download chat artifacts
        id: download-artifact-chat
        uses: dawidd6/action-download-artifact@v9
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          workflow: agent_gui_build.yml
          repo: smallcloudai/refact
          branch: ${{ inputs.chat_js_branch }}
          path: ./chat_package
          name: lts-refact-chat-js-.*\.tgz
          name_is_regexp: true
          
      - name: Prepare chat package
        shell: bash
        run: |
          mkdir -p ./chat_package_fixed
          find ./chat_package -name "*.tgz" -type f -exec cp {} ./chat_package_fixed/ \;
          ls -la ./chat_package_fixed

      - name: Install VSCE
        shell: bash
        run: |
          npm install ./chat_package_fixed/*.tgz
          npm install -g @vscode/vsce
          rm -rf ./chat_package ./chat_package_fixed

      - name: Package VSCE extension
        run: |
          chmod +x ./assets/refact-lsp*
          vsce package --target ${{ matrix.code-target }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: vscode-plugin-${{ matrix.target }}
          path: ./*.vsix
