@echo off
echo 🚀 AmrDev-AI Extension Builder
echo ================================
echo.

echo 📦 Step 1: Compiling TypeScript...
call npm run compile
if errorlevel 1 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)
echo ✅ Compilation successful!
echo.

echo 📦 Step 2: Creating VSIX package...
echo This might take a few minutes...
npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ VSIX creation failed!
    echo 🔧 Try the manual method instead:
    echo 1. Open VS Code
    echo 2. Press F5 to run in development mode
    pause
    exit /b 1
)

echo ✅ VSIX package created!
echo.

echo 🎉 Success! Look for the .vsix file in this folder
echo.
echo 📋 To install:
echo 1. Open VS Code
echo 2. Press Ctrl+Shift+P
echo 3. Type "Extensions: Install from VSIX..."
echo 4. Select the .vsix file
echo 5. Restart VS Code
echo 6. Look for "AmrDev-AI" in the sidebar!
echo.

dir *.vsix
echo.
pause
