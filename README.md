<p align="center">
  <img alt="AmrDev-AI" src="logo-small.png" width="128" height="128"/>
</p>

# AmrDev-AI - AI-Powered Code Assistant with Codestral Integration

---

[![GitHub](https://img.shields.io/github/stars/amrSayed1989/AmrDev-AI-Powered-VS-Code?style=social)](https://github.com/amrSayed1989/AmrDev-AI-Powered-VS-Code)
[![License](https://img.shields.io/github/license/amrSayed1989/AmrDev-AI-Powered-VS-Code)](https://github.com/amrSayed1989/AmrDev-AI-Powered-VS-Code/blob/main/LICENSE)
[![VS Code Marketplace](https://img.shields.io/visual-studio-marketplace/v/amrsayed.amrdev-ai-powered-vscode)](https://marketplace.visualstudio.com/items?itemName=amrsayed.amrdev-ai-powered-vscode)

## AmrDev-AI - Your Advanced AI Coding Companion

AmrDev-AI is an advanced AI-powered code assistant that integrates **Mistral AI's Codestral** for superior code completion, chat, and refactoring capabilities. Built by **Amr Sayed**, this extension enhances your coding experience with intelligent suggestions and seamless AI integration.

🚀 **Key Features:**
- **Codestral Integration**: Leverage Mistral AI's powerful Codestral model
- **Smart Code Completion**: Context-aware suggestions for 25+ programming languages
- **AI-Powered Chat**: Get coding help through natural language conversations
- **Real-time Analysis**: Instant feedback and code improvements
- **Custom API Key Support**: Use your own Codestral API key or the provided default
- **Multi-Language Support**: Python, JavaScript, Java, TypeScript, PHP, C++, C#, Go, Rust, and more

Suitable for both individual developers and teams, AmrDev-AI transforms your coding workflow with intelligent AI assistance.

*"AmrDev-AI with Codestral integration has revolutionized my coding experience - it's like having an expert pair programmer available 24/7!"*

## 🚀 How AmrDev-AI Empowers Developers

### 🤖 **Codestral-Powered Intelligence**
- **Advanced Code Completion** - Powered by Mistral AI's Codestral, AmrDev-AI provides precise, context-aware code suggestions that understand your project structure and coding patterns.
- **Intelligent Chat Assistant** - Get instant help with coding questions, explanations, and solutions through natural language conversations.
- **Multi-Model Support** - Choose between Codestral and other AI models based on your specific needs.

### 💡 **Smart Development Features**
- **Context-Aware Suggestions** - AI understands your entire codebase context for more relevant completions
- **Real-time Code Analysis** - Instant feedback on code quality, potential bugs, and improvements
- **Intelligent Refactoring** - Automated code improvements and optimizations
- **Custom Prompts** - Personalize AI responses to match your coding style and preferences

### 🔧 **Developer Tools Integration**
- **Seamless VS Code Integration** - Works natively within your favorite editor
- **Multi-Language Support** - Supports 25+ programming languages
- **AST & Vector Database** - Enhanced code understanding and semantic search
- **Custom API Keys** - Use your own Codestral API key for enhanced privacy and control

### 📋 **Powerful Commands**
- **@-commands** for enhanced context control:
    - **@file** - Reference specific files in your conversations
    - **@definition** - Find and attach function/class definitions
    - **@references** - Locate all references to symbols
    - **@workspace** - Search across your entire project
    - **@web** - Include web content for broader context

## 📦 Installation

1. **Install from VS Code Marketplace**:
   - Open VS Code
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "AmrDev-AI"
   - Click Install

2. **Install from VSIX**:
   - Download the latest `.vsix` file from [GitHub Releases](https://github.com/amrSayed1989/AmrDev-AI-Powered-VS-Code/releases)
   - Open VS Code
   - Press Ctrl+Shift+P and run "Extensions: Install from VSIX..."
   - Select the downloaded file

## ⚙️ Configuration

### 🔑 **Codestral Setup**

1. **Get Your API Key** (Optional):
   - Visit [Mistral AI Console](https://console.mistral.ai/)
   - Create an account and generate an API key
   - Or use the default provided key: `64c922gDh6Aab9AJvSGlGVT3kX4O87Ji`

2. **Configure AmrDev-AI**:
   - Open VS Code Settings (Ctrl+,)
   - Search for "AmrDev-AI"
   - Enable "Use Codestral": ✅
   - Enter your Codestral API Key (optional)

3. **Test Connection**:
   - Open Command Palette (Ctrl+Shift+P)
   - Run "AmrDev-AI: Test Codestral Connection"

### 📊 **Codestral Endpoints**

AmrDev-AI uses these Mistral AI endpoints:
- **Completion**: `https://codestral.mistral.ai/v1/fim/completions`
- **Chat**: `https://codestral.mistral.ai/v1/chat/completions`

### 🛠️ **Settings Reference**

| Setting | Description | Default |
|---------|-------------|----------|
| `amrdevai.useCodestral` | Enable Codestral integration | `false` |
| `amrdevai.codestralApiKey` | Your Codestral API key | `""` |
| `refactai.codeCompletionModel` | Model for code completion | `""` |
| `refactai.ast` | Enable AST features | `true` |
| `refactai.vecdb` | Enable vector database | `true` |
| `refactai.pauseCompletion` | Pause automatic completions | `false` |

## 🎯 Usage

### 📝 **Code Completion**
- Simply start typing and AmrDev-AI will provide intelligent suggestions
- Press `Alt+Space` for manual completion trigger
- Accept suggestions with `Tab`

### 💬 **AI Chat**
- Press `F1` to open the AI chat panel
- Ask questions about your code
- Get explanations, improvements, and solutions

### ⚡ **Quick Actions**
- Right-click in your code for context menu options
- Use "AmrDev-AI: ..." commands for various AI-powered actions

## 💻 Supported Languages

- JavaScript/TypeScript
- Python
- Java
- PHP
- Go
- C/C++
- C#
- Rust
- Ruby
- Swift
- Kotlin
- And many more!

## 🔒 Privacy & Security

- Your code is processed securely through Mistral AI's Codestral
- API keys are stored locally in VS Code settings
- No code is stored permanently on external servers
- You can use your own API key for enhanced privacy

## 👨‍💻 Developer

**Amr Sayed**
- GitHub: [@amrSayed1989](https://github.com/amrSayed1989)
- Email: <EMAIL>

## 🚀 Enterprise Solutions

1. **Refact.ai already understands your company's context:** AI Agent captures the unique structure, tools, and workflows of your organization, using your company's databases, documentation, and code architecture to deliver customized solutions.
2. **Gets smarter over time:** With each interaction and feedback, Refact.ai Agent adapts to your organization's needs, becoming more accurate and powerful.
3. **Organizes experience into the knowledge base:** Refact.ai Agent captures and shares knowledge from interactions with each team member. It not only streamlines workflows but also supports faster onboarding and smoother cross-functional collaboration across projects.

### Take full control of your AI Agent, tailored to your company:
- **Deploy Refact.ai on-premise:** on your own servers or private cloud. Your data never leaves your control. Telemetry from the plugins goes to your server and nowhere else. You can verify what the code does, it's open source.
- **Fine-tune a model on your codebase:** A fine-tuned code completion model will provide you with more relevant suggestions: it can memorize your coding style, the right way to use your internal APIs, and the tech stack you use.
- **Priority Support:** Our engineers are always available to assist you at every stage, from setup to fine-tuning and beyond.

**To get a 2-week free trial** for your team, just fill out the form on [our website](https://refact.ai/contact/?utm_source=vscode&utm_medium=marketplace&utm_campaign=enterprise). We'll reach out with more details!
\
&nbsp;
\
&nbsp;
## Which tasks can Refact.ai help me with?

- **Generate code from natural language prompts (even if you make typos)** - Instantly turn ideas into functional code, accelerating development and eliminating the blank-screen problem.
\
&nbsp;
  ![gen](https://github.com/user-attachments/assets/ef4acec7-4967-400a-900e-9d3382d05b1b)
\
&nbsp;
- **Refactor code** - Improve code quality, readability, and efficiency with automated refactoring that aligns with best practices.
\
&nbsp;
  ![refactor](https://github.com/user-attachments/assets/2cae4467-f363-4033-8ecf-2854dcc74aaa)
\
&nbsp;
- **Explain code** - Quickly understand complex or unfamiliar code with AI-generated explanations, making collaboration and onboarding faster.
\
&nbsp;
![explain](https://github.com/user-attachments/assets/bd43d9aa-15c9-49dc-9fa9-b5ab5c4ecdfe)
\
&nbsp;
- **Debug code** - Detect and fix errors faster with intelligent debugging, reducing time spent troubleshooting issues.
\
&nbsp;
![ddbg](https://github.com/user-attachments/assets/45e917b5-f47b-4b84-b1f4-f4918c8a00c7)
\
&nbsp;
- **Generate unit tests** -  Ensure code reliability by automatically creating comprehensive unit tests.
\
&nbsp;
  ![unit](https://github.com/user-attachments/assets/5168ee57-e35b-4484-bf19-70cc0f3a6299)
\
&nbsp;

- **Code Review** - Get AI-assisted code reviews for cleaner, more secure, and more efficient code, addressing developers' top concern: accuracy.
- **Create Documentation** - Automate documentation generation to keep knowledge accessible and up to date.
- **Generate Docstrings** - Enhance maintainability with clear, structured documentation generated for functions and classes in seconds.

## Join Our Discord Community

Connect with other developers in our [Discord community](https://www.smallcloud.ai/discord). Ask questions, share your opinion, propose new features.
