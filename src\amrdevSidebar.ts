import * as vscode from 'vscode';
import * as codestralAPI from './codestralAPI';
import * as codestralModels from './codestralModels';

export class AmrDevSidebarProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'amrdev-ai-sidebar';
    private _view?: vscode.WebviewView;

    constructor(private readonly _extensionUri: vscode.Uri) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'testConnection':
                    await this.testCodestralConnection();
                    break;
                case 'sendMessage':
                    await this.sendChatMessage(data.message);
                    break;
                case 'getCompletion':
                    await this.getCodeCompletion(data.prompt);
                    break;
                case 'openSettings':
                    await this.openSettings();
                    break;
                case 'refreshStatus':
                    await this.refreshStatus();
                    break;
                case 'showHelp':
                    await this.showHelp();
                    break;
            }
        });
    }

    private async testCodestralConnection() {
        try {
            const isConnected = await codestralAPI.CodestralAPI.testConnection();
            const status = codestralAPI.CodestralAPI.getStatus();

            this._view?.webview.postMessage({
                type: 'connectionResult',
                success: isConnected,
                status: status
            });
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'connectionResult',
                success: false,
                error: String(error)
            });
        }
    }

    private async sendChatMessage(message: string) {
        try {
            const response = await codestralAPI.CodestralAPI.getChatCompletion([
                { role: 'user', content: message }
            ]);

            this._view?.webview.postMessage({
                type: 'chatResponse',
                response: response
            });
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'chatResponse',
                error: String(error)
            });
        }
    }

    private async getCodeCompletion(prompt: string) {
        try {
            const completion = await codestralAPI.CodestralAPI.getCodeCompletion(prompt);

            this._view?.webview.postMessage({
                type: 'completionResponse',
                completion: completion
            });
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'completionResponse',
                error: String(error)
            });
        }
    }

    private async openSettings() {
        await vscode.commands.executeCommand(
            "workbench.action.openSettings",
            "@ext:amrsayed.amrdev-ai-powered-vscode"
        );
    }

    private async refreshStatus() {
        // Refresh the webview
        if (this._view) {
            this._view.webview.html = this._getHtmlForWebview(this._view.webview);
        }

        // Test connection and update status
        await this.testCodestralConnection();

        vscode.window.showInformationMessage("🔄 AmrDev-AI: Status refreshed!");
    }

    private async showHelp() {
        const selection = await vscode.window.showInformationMessage(
            "🚀 AmrDev-AI Help\n\nChoose what you'd like to learn about:",
            "Getting Started",
            "Codestral Setup",
            "Troubleshooting",
            "Documentation"
        );

        switch (selection) {
            case "Getting Started":
                vscode.window.showInformationMessage(
                    "🎆 Getting Started with AmrDev-AI:\n\n" +
                    "1. Enable Codestral in settings\n" +
                    "2. Test the connection\n" +
                    "3. Start coding and enjoy AI assistance!\n" +
                    "4. Use the chat for questions"
                );
                break;
            case "Codestral Setup":
                vscode.window.showInformationMessage(
                    "⚙️ Codestral Setup:\n\n" +
                    "1. Get API key from Mistral AI\n" +
                    "2. Go to Settings > AmrDev-AI\n" +
                    "3. Enable 'Use Codestral'\n" +
                    "4. Enter your API key (optional)\n" +
                    "5. Test connection"
                );
                break;
            case "Troubleshooting":
                vscode.window.showInformationMessage(
                    "🔧 Troubleshooting:\n\n" +
                    "- Check internet connection\n" +
                    "- Verify API key is correct\n" +
                    "- Try refreshing status\n" +
                    "- Restart VS Code if needed\n" +
                    "- Check Developer Console (Ctrl+Shift+I)"
                );
                break;
            case "Documentation":
                await vscode.commands.executeCommand(
                    "vscode.open",
                    vscode.Uri.parse("https://github.com/amrSayed1989/AmrDev-AI-Powered-VS-Code")
                );
                break;
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        const useCodestral = vscode.workspace.getConfiguration().get<boolean>("amrdevai.useCodestral");
        const models = codestralModels.getAvailableModels();
        const defaultModel = codestralModels.getDefaultModel();

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AmrDev-AI</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 16px;
        }

        .header {
            position: relative;
            text-align: center;
            margin-bottom: 20px;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            color: white;
        }

        .header-icons {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 8px;
        }

        .header-icon {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .header-icon:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }

        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
        }

        .status-card {
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 500;
        }

        .status-value {
            color: var(--vscode-textLink-foreground);
        }

        .status-success {
            color: #4CAF50;
        }

        .status-error {
            color: #f44336;
        }

        .button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 4px;
            width: calc(100% - 8px);
        }

        .button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .button-secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .button-secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .chat-container {
            margin-top: 20px;
        }

        .chat-input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-family: inherit;
            margin-bottom: 8px;
            resize: vertical;
            min-height: 60px;
        }

        .chat-messages {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 8px;
            background-color: var(--vscode-editor-background);
        }

        .message {
            margin-bottom: 12px;
            padding: 8px;
            border-radius: 4px;
        }

        .message-user {
            background-color: var(--vscode-textBlockQuote-background);
            border-left: 4px solid var(--vscode-textLink-foreground);
        }

        .message-ai {
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            border-left: 4px solid #4CAF50;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 16px 0;
        }

        .features-list li {
            padding: 4px 0;
            display: flex;
            align-items: center;
        }

        .features-list li::before {
            content: "✅";
            margin-right: 8px;
        }

        .models-section {
            margin-top: 16px;
        }

        .model-item {
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 8px;
        }

        .model-name {
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }

        .model-status {
            font-size: 12px;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-icons">
            <button class="header-icon" onclick="openSettings()" title="Settings">
                ⚙️
            </button>
            <button class="header-icon" onclick="refreshStatus()" title="Refresh">
                🔄
            </button>
            <button class="header-icon" onclick="showHelp()" title="Help">
                ❓
            </button>
        </div>
        <h1>🚀 AmrDev-AI</h1>
        <p>AI-Powered Code Assistant with Codestral Integration</p>
    </div>

    <div class="status-card">
        <h3>Status</h3>
        <div class="status-item">
            <span class="status-label">Codestral:</span>
            <span class="status-value ${useCodestral ? 'status-success' : 'status-error'}">
                ${useCodestral ? '✅ Enabled' : '❌ Disabled'}
            </span>
        </div>
        <div class="status-item">
            <span class="status-label">Models Available:</span>
            <span class="status-value">${models.length}</span>
        </div>
        <div class="status-item">
            <span class="status-label">Default Model:</span>
            <span class="status-value">${defaultModel || 'None'}</span>
        </div>
        <div class="status-item">
            <span class="status-label">Usage:</span>
            <span class="status-success">🆓 Unlimited</span>
        </div>
    </div>

    <div style="display: flex; gap: 8px; margin-bottom: 16px;">
        <button class="button" onclick="testConnection()" style="flex: 1;">🔧 Test Connection</button>
        <button class="button button-secondary" onclick="refreshStatus()" style="flex: 1;">🔄 Refresh</button>
    </div>

    <div class="features-list">
        <h3>Features</h3>
        <ul class="features-list">
            <li>Advanced Code Completion</li>
            <li>Intelligent Chat Assistant</li>
            <li>No Login Required</li>
            <li>Unlimited Usage</li>
            <li>Multi-Language Support</li>
        </ul>
    </div>

    <div class="models-section">
        <h3>Available Models</h3>
        ${models.map(model => `
            <div class="model-item">
                <div class="model-name">${model}</div>
                <div class="model-status">Ready</div>
            </div>
        `).join('')}
    </div>

    <div class="chat-container">
        <h3>Chat with AmrDev-AI</h3>
        <div class="chat-messages" id="chatMessages">
            <div class="message message-ai">
                <strong>AmrDev-AI:</strong> Hello! I'm ready to help you with coding. Ask me anything about programming, code completion, or debugging!
            </div>
        </div>
        <textarea class="chat-input" id="chatInput" placeholder="Type your message here... (e.g., 'How do I create a function in JavaScript?')"></textarea>
        <button class="button" onclick="sendMessage()">💬 Send Message</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function testConnection() {
            vscode.postMessage({ type: 'testConnection' });
        }

        function openSettings() {
            vscode.postMessage({ type: 'openSettings' });
        }

        function refreshStatus() {
            vscode.postMessage({ type: 'refreshStatus' });
        }

        function showHelp() {
            vscode.postMessage({ type: 'showHelp' });
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage('user', message);
            input.value = '';

            // Send to extension
            vscode.postMessage({ type: 'sendMessage', message: message });
        }

        function addMessage(sender, content) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-' + sender;
            messageDiv.innerHTML = '<strong>' + (sender === 'user' ? 'You' : 'AmrDev-AI') + ':</strong> ' + content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.type) {
                case 'connectionResult':
                    if (message.success) {
                        addMessage('ai', '✅ Codestral connection successful! Ready to assist you.');
                    } else {
                        addMessage('ai', '❌ Connection failed: ' + (message.error || 'Unknown error'));
                    }
                    break;

                case 'chatResponse':
                    if (message.error) {
                        addMessage('ai', '❌ Error: ' + message.error);
                    } else {
                        addMessage('ai', message.response);
                    }
                    break;
            }
        });

        // Enter key to send message
        document.getElementById('chatInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>`;
    }
}
