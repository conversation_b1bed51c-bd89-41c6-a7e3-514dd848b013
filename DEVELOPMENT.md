# AmrDev-AI Development Guide

This guide explains how to set up, develop, and contribute to the AmrDev-AI VS Code extension.

## 🏗️ Architecture Overview

AmrDev-AI is built on top of the Refact.ai foundation with enhanced Codestral integration:

```
src/
├── codestralAPI.ts      # Codestral API integration
├── fetchAPI.ts          # Enhanced API handling
├── extension.ts         # Main extension entry point
├── launchRust.ts        # Rust binary management
├── quickProvider.ts     # Quick actions provider
├── sidebar.ts           # Side panel management
├── statusBar.ts         # Status bar integration
├── userLogin.ts         # Authentication handling
└── ...                  # Other core files
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 16+ 
- npm or yarn
- VS Code
- Git

### Installation
```bash
# Clone the repository
git clone https://github.com/amrSayed1989/AmrDev-AI-Powered-VS-Code.git
cd AmrDev-AI-Powered-VS-Code

# Install dependencies
npm install

# Compile TypeScript
npm run compile
```

### Development Workflow
```bash
# Watch mode for development
npm run watch

# Run tests
npm test

# Lint code
npm run lint

# Package extension
npm run package
```

## 🔧 Key Components

### Codestral Integration (`src/codestralAPI.ts`)

The main Codestral integration handles:
- Code completion requests
- Chat completions
- API key management
- Connection testing

```typescript
export class CodestralAPI {
    private static readonly COMPLETION_ENDPOINT = "https://codestral.mistral.ai/v1/fim/completions";
    private static readonly CHAT_ENDPOINT = "https://codestral.mistral.ai/v1/chat/completions";
    private static readonly DEFAULT_API_KEY = "64c922gDh6Aab9AJvSGlGVT3kX4O87Ji";
    
    // Implementation details...
}
```

### Enhanced Fetch API (`src/fetchAPI.ts`)

Extended with Codestral-specific functions:
- `fetch_code_completion_codestral()`: Code completion via Codestral
- `fetch_chat_codestral()`: Chat completion via Codestral

### Settings Configuration

New settings added to `package.json`:
```json
{
    "amrdevai.useCodestral": {
        "type": "boolean",
        "default": false,
        "description": "Enable Codestral integration"
    },
    "amrdevai.codestralApiKey": {
        "type": "string", 
        "default": "",
        "description": "Codestral API Key from Mistral AI"
    }
}
```

## 🧪 Testing

### Manual Testing
1. Install the extension in development mode
2. Enable Codestral in settings
3. Test code completion and chat features
4. Run the connection test command

### Automated Testing
```bash
# Run all tests
npm test

# Run specific test suite
npm run test:unit
npm run test:integration
```

### Testing Codestral Integration
```bash
# Test API connection
Command Palette > "AmrDev-AI: Test Codestral Connection"
```

## 📦 Building and Packaging

### Development Build
```bash
npm run compile
```

### Production Package
```bash
# Install VSCE if not already installed
npm install -g @vscode/vsce

# Package the extension
vsce package

# This creates: amrdev-ai-powered-vscode-6.5.8.vsix
```

### Publishing
```bash
# Publish to VS Code Marketplace
vsce publish

# Or publish a pre-release
vsce publish --pre-release
```

## 🔄 Configuration Management

### Extension Settings
The extension uses VS Code's configuration API:

```typescript
// Get Codestral settings
const useCodestral = vscode.workspace.getConfiguration().get<boolean>("amrdevai.useCodestral");
const apiKey = vscode.workspace.getConfiguration().get<string>("amrdevai.codestralApiKey");

// Update settings
await vscode.workspace.getConfiguration().update('amrdevai.useCodestral', true, vscode.ConfigurationTarget.Global);
```

### Backward Compatibility
The extension maintains compatibility with original Refact.ai settings while adding new AmrDev-AI specific configurations.

## 🐛 Debugging

### VS Code Debugging
1. Open the project in VS Code
2. Press F5 to launch Extension Development Host
3. Set breakpoints in TypeScript files
4. Test functionality in the new VS Code window

### Console Logging
```typescript
console.log(["AmrDev-AI Debug:", data]);
```

### Error Handling
```typescript
try {
    const result = await codestralAPI.CodestralAPI.getCodeCompletion(prompt);
    return result;
} catch (error) {
    console.error("Codestral error:", error);
    vscode.window.showErrorMessage(`AmrDev-AI: ${error.message}`);
}
```

## 🤝 Contributing

### Code Style
- Use TypeScript strict mode
- Follow existing code patterns
- Add JSDoc comments for public APIs
- Use meaningful variable names

### Commit Messages
```
feat: add Codestral chat integration
fix: resolve API key validation issue
docs: update README with new features
refactor: improve error handling
```

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Update documentation
6. Submit a pull request

## 📚 API Reference

### Codestral API Endpoints

#### Code Completion
```
POST https://codestral.mistral.ai/v1/fim/completions
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY

{
    "model": "codestral-latest",
    "prompt": "function fibonacci(",
    "suffix": ") {\n    return fib(n-1) + fib(n-2);\n}",
    "max_tokens": 100,
    "temperature": 0.1
}
```

#### Chat Completion
```
POST https://codestral.mistral.ai/v1/chat/completions
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY

{
    "model": "codestral-latest",
    "messages": [
        {"role": "user", "content": "How do I implement a binary search?"}
    ],
    "max_tokens": 1000,
    "temperature": 0.7
}
```

## 🔐 Security Considerations

### API Key Handling
- API keys are stored in VS Code's secure settings
- Never log API keys to console
- Provide option for users to use their own keys
- Default key is provided for convenience but users should use their own for production

### Data Privacy
- Code is sent to Mistral AI's Codestral service
- No permanent storage of user code on external servers
- Users can opt out by disabling Codestral integration

## 📈 Performance Optimization

### Caching
- Implement response caching for repeated requests
- Cache model capabilities and settings

### Rate Limiting
- Respect API rate limits
- Implement exponential backoff for failed requests
- Queue requests to avoid overwhelming the API

### Memory Management
- Clean up event listeners on extension deactivation
- Dispose of resources properly
- Monitor memory usage in development

## 🚀 Deployment

### Release Process
1. Update version in `package.json`
2. Update `CHANGELOG.md`
3. Create git tag
4. Build and test the package
5. Publish to VS Code Marketplace
6. Create GitHub release

### Environment Variables
```bash
# For publishing
VSCE_PAT=your_personal_access_token
```

## 📞 Support

### Getting Help
- Check the [GitHub Issues](https://github.com/amrSayed1989/AmrDev-AI-Powered-VS-Code/issues)
- Read the [README.md](README.md)
- Contact: <EMAIL>

### Reporting Bugs
Please include:
- VS Code version
- Extension version
- Steps to reproduce
- Error messages
- System information

---

**Happy coding with AmrDev-AI! 🚀**
