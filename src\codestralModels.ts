import * as vscode from 'vscode';

export interface CodestralModel {
    name: string;
    displayName: string;
    description: string;
    maxTokens: number;
    temperature: number;
    available: boolean;
}

export const CODESTRAL_MODELS: CodestralModel[] = [
    {
        name: "codestral-2501",
        displayName: "Codestral 2501",
        description: "Latest Codestral model - Enhanced performance and capabilities",
        maxTokens: 32768,
        temperature: 0.1,
        available: true
    },
    {
        name: "codestral-latest",
        displayName: "Codestral Latest",
        description: "Latest version of Codestral - Best performance and features",
        maxTokens: 32768,
        temperature: 0.1,
        available: true
    },
    {
        name: "codestral-2024-05-08",
        displayName: "Codestral 2024-05-08",
        description: "Stable version of Codestral - Reliable and tested",
        maxTokens: 32768,
        temperature: 0.1,
        available: true
    }
];

export function getAvailableModels(): string[] {
    const useCodestral = vscode.workspace.getConfiguration().get<boolean>("amrdevai.useCodestral");

    if (useCodestral) {
        console.log("[AmrDev-AI] Returning Codestral models");
        return CODESTRAL_MODELS.filter(m => m.available).map(m => m.name);
    }

    console.log("[AmrDev-AI] Codestral disabled, returning empty models");
    return [];
}

export function getDefaultModel(): string {
    const useCodestral = vscode.workspace.getConfiguration().get<boolean>("amrdevai.useCodestral");

    if (useCodestral) {
        return "codestral-2501";
    }

    return "";
}

export function getModelInfo(modelName: string): CodestralModel | undefined {
    return CODESTRAL_MODELS.find(m => m.name === modelName);
}

export function isCodestralModel(modelName: string): boolean {
    return CODESTRAL_MODELS.some(m => m.name === modelName);
}

export function initializeCodestralModels(): void {
    const useCodestral = vscode.workspace.getConfiguration().get<boolean>("amrdevai.useCodestral");

    if (useCodestral) {
        // Set global models for AmrDev-AI
        global.chat_models = getAvailableModels();
        global.chat_default_model = getDefaultModel();
        global.have_caps = true;

        console.log("[AmrDev-AI] Initialized Codestral models:", global.chat_models);
        console.log("[AmrDev-AI] Default model:", global.chat_default_model);

        // Show success message
        vscode.window.setStatusBarMessage("$(rocket) AmrDev-AI: Codestral models loaded!", 3000);
    } else {
        // Clear models when Codestral is disabled
        global.chat_models = [];
        global.chat_default_model = "";
        global.have_caps = false;

        console.log("[AmrDev-AI] Codestral disabled, models cleared");
    }
}
