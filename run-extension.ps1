# AmrDev-AI Extension Test Script
Write-Host "🚀 Starting AmrDev-AI Extension Development..." -ForegroundColor Green
Write-Host ""

# Check if VS Code is available
$vscodePath = Get-Command code -ErrorAction SilentlyContinue
if (-not $vscodePath) {
    Write-Host "❌ VS Code not found in PATH" -ForegroundColor Red
    Write-Host "Please install VS Code or add it to your PATH" -ForegroundColor Yellow
    exit 1
}

# Compile TypeScript
Write-Host "📦 Compiling TypeScript..." -ForegroundColor Blue
npm run compile
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ TypeScript compilation failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Compilation successful!" -ForegroundColor Green
Write-Host ""

# Start VS Code with extension development
Write-Host "🔧 Starting VS Code Extension Development Host..." -ForegroundColor Blue
Write-Host "This will open a new VS Code window with AmrDev-AI loaded" -ForegroundColor Yellow
Write-Host ""

# Launch VS Code in extension development mode
code --extensionDevelopmentPath=. --new-window

Write-Host "🎉 VS Code launched!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps in the new VS Code window:" -ForegroundColor Cyan
Write-Host "1. Look for 'AmrDev-AI' in the sidebar" -ForegroundColor White
Write-Host "2. Open Settings (Ctrl+,) and search for 'AmrDev-AI'" -ForegroundColor White
Write-Host "3. Enable 'Use Codestral' setting" -ForegroundColor White
Write-Host "4. Test connection: Ctrl+Shift+P → 'AmrDev-AI: Test Codestral Connection'" -ForegroundColor White
Write-Host "5. Try chat: Press F1" -ForegroundColor White
Write-Host ""
Write-Host "🔑 Default Codestral API Key is already configured!" -ForegroundColor Green
