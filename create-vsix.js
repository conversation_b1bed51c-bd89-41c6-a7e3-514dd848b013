const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Creating AmrDev-AI VSIX package...');

// Check if we have the compiled files
if (!fs.existsSync('out')) {
    console.log('📦 Compiling TypeScript...');
    try {
        execSync('npx tsc -p ./', { stdio: 'inherit' });
        console.log('✅ TypeScript compiled successfully!');
    } catch (error) {
        console.error('❌ TypeScript compilation failed:', error.message);
        process.exit(1);
    }
}

// Create a simple VSIX using npx
console.log('📦 Creating VSIX package...');
try {
    // Try to use npx vsce directly
    execSync('npx @vscode/vsce package --no-dependencies', { stdio: 'inherit' });
    console.log('✅ VSIX package created successfully!');
    
    // List the created file
    const files = fs.readdirSync('.').filter(f => f.endsWith('.vsix'));
    if (files.length > 0) {
        console.log('🎉 VSIX file created:', files[0]);
        console.log('📍 Location:', path.resolve(files[0]));
        console.log('');
        console.log('📋 To install:');
        console.log('1. Open VS Code');
        console.log('2. Press Ctrl+Shift+P');
        console.log('3. Type "Extensions: Install from VSIX..."');
        console.log('4. Select the .vsix file');
        console.log('5. Restart VS Code');
        console.log('6. Look for "AmrDev-AI" in the sidebar!');
    }
} catch (error) {
    console.error('❌ VSIX creation failed:', error.message);
    console.log('');
    console.log('🔧 Manual alternative:');
    console.log('1. Open VS Code');
    console.log('2. Press F5 to run in development mode');
    console.log('3. This will open a new window with AmrDev-AI loaded');
}
