# Changelog

All notable changes to the AmrDev-AI extension will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [6.5.8] - 2025-01-15

### Added - AmrDev-AI Initial Release
- 🚀 **Codestral Integration**: Full integration with Mistral AI's Codestral model
- 🤖 **Advanced Code Completion**: Context-aware code suggestions powered by Codestral
- 💬 **AI-Powered Chat**: Natural language conversations for coding assistance
- 🔧 **Custom API Key Support**: Use your own Codestral API key or the provided default
- ⚙️ **New Settings**:
  - `amrdevai.useCodestral`: Enable/disable Codestral integration
  - `amrdevai.codestralApiKey`: Configure your Codestral API key
- 🧪 **Connection Testing**: New command to test Codestral API connectivity
- 🎨 **Rebranded UI**: Updated all references from "Refact.ai" to "AmrDev-AI"
- 📚 **Enhanced Documentation**: Comprehensive README with setup instructions

### Changed
- 🏷️ **Extension Identity**: 
  - Name: `amrdev-ai-powered-vscode`
  - Publisher: `amrsayed`
  - Display Name: "AmrDev-AI – AI-Powered Code Assistant with Codestral Integration"
- 🎯 **Command Categories**: All commands now use "AmrDev-AI" category
- 🔗 **Repository Links**: Updated to point to AmrDev-AI GitHub repository
- 👨‍💻 **Author Information**: Updated to Amr Sayed
- 🏠 **Homepage**: Updated to GitHub repository
- 📧 **Support Email**: Updated contact information

### Technical Changes
- 🔌 **API Integration**: Added `codestralAPI.ts` for Codestral communication
- 🔄 **Fetch Functions**: New Codestral-specific completion and chat functions
- 🧹 **Code Cleanup**: Updated extension references throughout codebase
- 🔧 **Settings Management**: Enhanced logout to clear AmrDev-AI specific settings
- 📊 **Status Reporting**: Improved error handling and user feedback

### Endpoints
- **Completion**: `https://codestral.mistral.ai/v1/fim/completions`
- **Chat**: `https://codestral.mistral.ai/v1/chat/completions`

### Commands Added
- `amrdevai.testCodestral`: Test Codestral API connection

### Backward Compatibility
- ✅ Maintains compatibility with existing Refact.ai configurations
- ✅ Fallback support for original extension name detection
- ✅ Preserves all original functionality while adding Codestral features

### Developer Notes
- Built on the solid foundation of Refact.ai
- Enhanced with Mistral AI's Codestral for superior code intelligence
- Designed for both individual developers and enterprise teams
- Supports 25+ programming languages

---

## Previous Versions (Refact.ai Base)

This extension is built upon the excellent Refact.ai foundation. For previous version history, please refer to the original Refact.ai changelog.

### Credits
- Original codebase: Refact.ai team
- Codestral integration: Amr Sayed
- AI Model: Mistral AI's Codestral
